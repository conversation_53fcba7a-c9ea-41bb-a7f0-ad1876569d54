import { useEffect, useRef, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface PreviewProps {
  project: any;
  clips: any[];
  currentTime: number;
  isPlaying: boolean;
  onTimeUpdate: (time: number) => void;
  onPlayPause: () => void;
}

export function Preview({
  project,
  clips,
  currentTime,
  isPlaying,
  onTimeUpdate,
  onPlayPause
}: PreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentClip, setCurrentClip] = useState<any>(null);
  const [clipUrls, setClipUrls] = useState<Record<string, string>>({});

  const aspectRatio = project.resolution.width / project.resolution.height;

  // Get URLs for all video clips using Convex storage
  useEffect(() => {
    const loadClipUrls = async () => {
      const urls: Record<string, string> = {};
      for (const clip of clips.filter(c => c.type === "video")) {
        try {
          // Fetch the file directly from Convex storage endpoint
          const response = await fetch(`http://127.0.0.1:3210/storage/${clip.fileId}`, {
            method: 'GET',
            redirect: 'follow'
          });

          if (response.ok) {
            // Create a blob URL for the video
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            urls[clip._id] = blobUrl;
            console.log(`Loaded video URL for ${clip.name}:`, blobUrl);
          } else {
            console.error(`Failed to load clip URL for ${clip.name}:`, response.status, response.statusText);
          }
        } catch (error) {
          console.error(`Failed to load clip URL for ${clip.name}:`, error);
        }
      }
      setClipUrls(urls);
    };

    if (clips.length > 0) {
      loadClipUrls();
    }

    return () => {
      // Cleanup blob URLs
      Object.values(clipUrls).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [clips]);

  // Find the current clip based on timeline position
  useEffect(() => {
    const activeClip = clips.find(clip =>
      currentTime >= clip.startTime &&
      currentTime < clip.startTime + (clip.trimEnd - clip.trimStart)
    );

    if (activeClip !== currentClip) {
      setCurrentClip(activeClip);
    }
  }, [currentTime, clips, currentClip]);

  // Update video element when current clip changes
  useEffect(() => {
    if (videoRef.current && currentClip && clipUrls[currentClip._id]) {
      videoRef.current.src = clipUrls[currentClip._id];
      const clipTime = currentTime - currentClip.startTime + currentClip.trimStart;
      videoRef.current.currentTime = clipTime;
    }
  }, [currentClip, clipUrls, currentTime]);

  // Handle play/pause
  useEffect(() => {
    if (videoRef.current) {
      if (isPlaying && currentClip) {
        videoRef.current.play();
      } else {
        videoRef.current.pause();
      }
    }
  }, [isPlaying, currentClip]);

  const handleTimeUpdate = () => {
    if (videoRef.current && currentClip) {
      const videoTime = videoRef.current.currentTime;
      const timelineTime = currentClip.startTime + videoTime - currentClip.trimStart;
      onTimeUpdate(timelineTime);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const maxDuration = Math.max(30, ...clips.map(c => c.startTime + (c.trimEnd - c.trimStart)));

  return (
    <div className="h-full flex items-center justify-center p-8">
      <div className="relative bg-black rounded-lg overflow-hidden shadow-2xl">
        <div
          className="relative bg-navy-900 flex items-center justify-center"
          style={{
            width: `${Math.min(800, window.innerWidth * 0.6)}px`,
            height: `${Math.min(800, window.innerWidth * 0.6) / aspectRatio}px`,
          }}
        >
          {/* Video Element */}
          <video
            ref={videoRef}
            className="w-full h-full object-contain"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={() => {
              if (videoRef.current && currentClip) {
                const clipTime = currentTime - currentClip.startTime + currentClip.trimStart;
                videoRef.current.currentTime = clipTime;
              }
            }}
            controls={false}
            playsInline
          />

          {/* Canvas for compositing (future enhancement) */}
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full pointer-events-none"
            width={project.resolution.width}
            height={project.resolution.height}
            style={{ display: 'none' }}
          />

          {/* Placeholder when no video */}
          {!currentClip && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-6xl mb-4">🎬</div>
                <p className="text-lg">Preview Window</p>
                <p className="text-sm opacity-75">
                  {project.resolution.width} × {project.resolution.height}
                </p>
                <p className="text-xs opacity-50 mt-2">
                  Add clips to the timeline to see preview
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Preview Controls */}
        <div className="absolute bottom-4 left-4 right-4 bg-black/70 backdrop-blur-sm rounded-lg p-3">
          <div className="flex items-center gap-3">
            <button
              onClick={onPlayPause}
              className="text-white hover:text-accent-blue transition-colors text-xl"
            >
              {isPlaying ? "⏸️" : "▶️"}
            </button>

            <div className="flex-1 bg-gray-700 rounded-full h-2 cursor-pointer"
              onClick={(e) => {
                const rect = e.currentTarget.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const percentage = x / rect.width;
                const newTime = percentage * maxDuration;
                onTimeUpdate(newTime);
              }}
            >
              <div
                className="bg-accent-blue h-2 rounded-full transition-all"
                style={{ width: `${(currentTime / maxDuration) * 100}%` }}
              />
            </div>

            <span className="text-white text-sm font-mono">
              {formatTime(currentTime)} / {formatTime(maxDuration)}
            </span>

            <button className="text-white hover:text-accent-blue transition-colors">
              🔊
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
