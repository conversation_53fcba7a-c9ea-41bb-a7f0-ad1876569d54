import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState, useRef } from "react";
import { toast } from "sonner";

interface MediaLibraryProps {
  projectId: Id<"projects">;
}

export function MediaLibrary({ projectId }: MediaLibraryProps) {
  const generateUploadUrl = useMutation(api.storage.generateUploadUrl);
  const addClip = useMutation(api.clips.add);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadInfo, setUploadInfo] = useState<Record<string, { size: number; loaded: number; startTime: number }>>({});
  const [activeUploads, setActiveUploads] = useState<Record<string, XMLHttpRequest>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    const allowedTypes = [
      'video/mp4', 'video/mov', 'video/avi', 'video/webm',
      'audio/mp3', 'audio/wav', 'audio/aac', 'audio/ogg',
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
    ];

    if (!allowedTypes.includes(file.type)) {
      return `File "${file.name}" has unsupported format. Supported formats: MP4, MOV, AVI, WebM, MP3, WAV, AAC, OGG, JPEG, PNG, GIF, WebP.`;
    }

    return null;
  };

  const getFileDuration = (file: File): Promise<number> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('video/')) {
        const video = document.createElement('video');
        video.preload = 'metadata';
        video.onloadedmetadata = () => {
          resolve(video.duration || 10);
          URL.revokeObjectURL(video.src);
        };
        video.onerror = () => resolve(10);
        video.src = URL.createObjectURL(file);
      } else if (file.type.startsWith('audio/')) {
        const audio = document.createElement('audio');
        audio.preload = 'metadata';
        audio.onloadedmetadata = () => {
          resolve(audio.duration || 10);
          URL.revokeObjectURL(audio.src);
        };
        audio.onerror = () => resolve(10);
        audio.src = URL.createObjectURL(file);
      } else {
        // Images default to 5 seconds
        resolve(5);
      }
    });
  };

  const handleFileUpload = async (files: FileList) => {
    if (files.length === 0) return;

    setUploading(true);
    const newProgress: Record<string, number> = {};

    try {
      for (const file of Array.from(files)) {
        const validationError = validateFile(file);
        if (validationError) {
          toast.error(validationError);
          continue;
        }

        // Show file size info for large files
        const fileSizeMB = file.size / (1024 * 1024);
        if (fileSizeMB > 50) {
          toast.info(`Uploading large file: ${file.name} (${fileSizeMB.toFixed(1)}MB). This may take a while...`);
        }

        newProgress[file.name] = 0;
        setUploadProgress(prev => ({ ...prev, ...newProgress }));

        // Initialize upload info
        const startTime = Date.now();
        setUploadInfo(prev => ({
          ...prev,
          [file.name]: { size: file.size, loaded: 0, startTime }
        }));

        const uploadUrl = await generateUploadUrl();

        // Create XMLHttpRequest for real progress tracking
        const result = await new Promise<Response>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          // Store the xhr for potential cancellation
          setActiveUploads(prev => ({ ...prev, [file.name]: xhr }));

          // Track upload progress
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const percentComplete = (event.loaded / event.total) * 100;
              setUploadProgress(prev => ({
                ...prev,
                [file.name]: Math.min(percentComplete, 99) // Keep at 99% until complete
              }));

              // Update upload info for speed calculation
              setUploadInfo(prev => ({
                ...prev,
                [file.name]: { ...prev[file.name], loaded: event.loaded }
              }));
            }
          });

          xhr.addEventListener('load', () => {
            // Clean up active upload reference
            setActiveUploads(prev => {
              const newPrev = { ...prev };
              delete newPrev[file.name];
              return newPrev;
            });

            if (xhr.status >= 200 && xhr.status < 300) {
              resolve(new Response(xhr.responseText, {
                status: xhr.status,
                statusText: xhr.statusText,
                headers: new Headers(xhr.getAllResponseHeaders().split('\r\n').reduce((headers, line) => {
                  const [key, value] = line.split(': ');
                  if (key && value) headers[key] = value;
                  return headers;
                }, {} as Record<string, string>))
              }));
            } else {
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          });

          xhr.addEventListener('error', () => {
            setActiveUploads(prev => {
              const newPrev = { ...prev };
              delete newPrev[file.name];
              return newPrev;
            });
            reject(new Error('Upload failed'));
          });

          xhr.addEventListener('timeout', () => {
            setActiveUploads(prev => {
              const newPrev = { ...prev };
              delete newPrev[file.name];
              return newPrev;
            });
            reject(new Error('Upload timed out'));
          });

          xhr.addEventListener('abort', () => {
            setActiveUploads(prev => {
              const newPrev = { ...prev };
              delete newPrev[file.name];
              return newPrev;
            });
            reject(new Error('Upload cancelled'));
          });

          xhr.open('POST', uploadUrl);
          xhr.setRequestHeader('Content-Type', file.type);
          xhr.timeout = 300000; // 5 minute timeout for large files
          xhr.send(file);
        });

        if (!result.ok) {
          throw new Error(`Upload failed for ${file.name}: ${result.statusText}`);
        }

        const { storageId } = await result.json();

        // Determine file type and get duration
        const type = file.type.startsWith("video/") ? "video" :
                    file.type.startsWith("audio/") ? "audio" : "image";

        const duration = await getFileDuration(file);

        await addClip({
          projectId,
          name: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
          fileId: storageId,
          type,
          duration,
          startTime: 0,
          track: 0,
        });

        setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
        toast.success(`${file.name} uploaded successfully`);

        // Remove from progress after a delay
        setTimeout(() => {
          setUploadProgress(prev => {
            const newPrev = { ...prev };
            delete newPrev[file.name];
            return newPrev;
          });
          setUploadInfo(prev => {
            const newPrev = { ...prev };
            delete newPrev[file.name];
            return newPrev;
          });
        }, 2000);
      }
    } catch (error) {
      toast.error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error(error);

      // Clean up progress for failed uploads
      setUploadProgress({});
      setUploadInfo({});
      setActiveUploads({});
    } finally {
      setUploading(false);
    }
  };

  const cancelUpload = (filename: string) => {
    const xhr = activeUploads[filename];
    if (xhr) {
      xhr.abort();
      toast.info(`Upload cancelled: ${filename}`);
    }

    // Clean up progress and info
    setUploadProgress(prev => {
      const newPrev = { ...prev };
      delete newPrev[filename];
      return newPrev;
    });
    setUploadInfo(prev => {
      const newPrev = { ...prev };
      delete newPrev[filename];
      return newPrev;
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    handleFileUpload(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const addColorClip = async () => {
    try {
      // Create a simple colored canvas and convert to blob
      const canvas = document.createElement('canvas');
      canvas.width = 1920;
      canvas.height = 1080;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#3b82f6'; // Blue color
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      canvas.toBlob(async (blob) => {
        if (blob) {
          const uploadUrl = await generateUploadUrl();
          const result = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": "image/png" },
            body: blob,
          });

          if (result.ok) {
            const { storageId } = await result.json();
            await addClip({
              projectId,
              name: "Color Clip",
              fileId: storageId,
              type: "image",
              duration: 5,
              startTime: 0,
              track: 0,
            });
            toast.success("Color clip added");
          }
        }
      }, 'image/png');
    } catch (error) {
      toast.error("Failed to add color clip");
    }
  };

  return (
    <div className="p-4 h-full flex flex-col">
      <div className="mb-4">
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading}
          className="w-full px-4 py-3 bg-accent-blue hover:bg-accent-blue-dark disabled:opacity-50 rounded-lg font-medium transition-colors"
        >
          {uploading ? "Uploading..." : "Upload Media"}
        </button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="video/*,audio/*,image/*"
          onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="mb-4 space-y-2">
          {Object.entries(uploadProgress).map(([filename, progress]) => {
            const info = uploadInfo[filename];
            let speedText = '';
            let sizeText = '';

            if (info) {
              const elapsedTime = (Date.now() - info.startTime) / 1000; // seconds
              const speed = info.loaded / elapsedTime; // bytes per second
              const speedMBps = speed / (1024 * 1024); // MB per second
              const sizeMB = info.size / (1024 * 1024);
              const loadedMB = info.loaded / (1024 * 1024);

              if (speedMBps > 0.1) {
                speedText = ` • ${speedMBps.toFixed(1)} MB/s`;
              }
              sizeText = ` • ${loadedMB.toFixed(1)}/${sizeMB.toFixed(1)} MB`;
            }

            return (
              <div key={filename} className="bg-navy-700 rounded p-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-xs text-gray-300 truncate flex-1">{filename}</div>
                  <div className="flex items-center gap-2">
                    <div className="text-xs text-gray-400">
                      {progress.toFixed(1)}%{speedText}{sizeText}
                    </div>
                    {progress < 100 && (
                      <button
                        onClick={() => cancelUpload(filename)}
                        className="text-xs text-red-400 hover:text-red-300 px-2 py-1 rounded bg-red-900/20 hover:bg-red-900/40 transition-colors"
                        title="Cancel upload"
                      >
                        ✕
                      </button>
                    )}
                  </div>
                </div>
                <div className="w-full bg-navy-600 rounded-full h-2">
                  <div
                    className="bg-accent-blue h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className="flex-1 border-2 border-dashed border-navy-600 rounded-lg p-8 text-center hover:border-accent-blue transition-colors"
      >
        <div className="text-4xl mb-4">📁</div>
        <p className="text-gray-300 mb-2">Drag and drop media files here</p>
        <p className="text-sm text-gray-400 mb-4">
          Supports video (MP4, MOV, AVI, WebM), audio (MP3, WAV, AAC, OGG), and images (JPEG, PNG, GIF, WebP)
        </p>
        <p className="text-xs text-gray-500">
          No file size limit - upload videos of any size
        </p>
      </div>

      <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-300 mb-2">Quick Add</h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={addColorClip}
            className="px-3 py-2 bg-navy-700 hover:bg-navy-600 rounded text-sm transition-colors"
          >
            🎨 Color Clip
          </button>
          <button className="px-3 py-2 bg-navy-700 hover:bg-navy-600 rounded text-sm transition-colors">
            📝 Text Title
          </button>
        </div>
      </div>
    </div>
  );
}
